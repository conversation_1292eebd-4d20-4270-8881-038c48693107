{"name": "forever-fest-2026", "version": "1.0.0", "private": true, "sideEffects": false, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b", "engines": {"node": ">=22", "pnpm": ">=10", "npm": "please-use-pnpm", "yarn": "please-use-pnpm"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "prod": "next build && next start", "analyze": "ANALYZE=true next build", "start": "next start", "typecheck": "tsc --noEmit", "lint": "next lint --fix", "validate": "pnpm typecheck && pnpm lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-tooltip": "1.1.6", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "add-to-calendar-button-react": "^2.9.1", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.454.0", "next": "^15.4.5", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-medium-image-zoom": "^5.3.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@next/bundle-analyzer": "^15.4.5", "@next/eslint-plugin-next": "^15.4.5", "@stylistic/eslint-plugin": "^5.2.2", "@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-next": "15.3.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}, "browserslist": ["Chrome >= 109", "Firefox >= 109", "Safari >= 16.4", "Edge >= 109"]}